Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-abaqus-toolkit\scripts\sG1D_v3DB.py", line 343, in updateComboBox_sSections
    for name, section in mdb.models[modelName].sections.items():
  File "SMAPyrModules\SMAPyrAbqRpoPy.m\src\abqRpoClient.py", line 536, in __call__
  File "SMAPyrModules\SMAPyrAbqRpoPy.m\src\abqRpoClient.py", line 946, in call
  File "SMAPyrModules\SMAPyrAbqRpoPy.m\src\abqRpoClient.py", line 630, in __call__
  File "SMAPyrModules\SMAPyrAbqRpoPy.m\src\abqRpoClient.py", line 761, in __request
TypeError: Cannot create a consistent method resolution
order (MRO) for bases aba_type
