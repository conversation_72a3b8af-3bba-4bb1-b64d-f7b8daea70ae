Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-abaqus-toolkit\scripts\sg2DLaminateDB.py", line 140, in updateComboBox_1Sections
    for name, section in mdb.models[modelName].sections.items():
  File "SMAPyrModules\SMAPyrAbqRpoPy.m\src\abqRpoClient.py", line 536, in __call__
  File "SMAPyrModules\SMAPyrAbqRpoPy.m\src\abqRpoClient.py", line 946, in call
  File "SMAPyrModules\SMAPyrAbqRpoPy.m\src\abqRpoClient.py", line 630, in __call__
  File "SMAPyrModules\SMAPyrAbqRpoPy.m\src\abqRpoClient.py", line 761, in __request
TypeError: Cannot create a consistent method resolution
order (MRO) for bases aba_type
Traceback (most recent call last):
  File "C:\Users\<USER>\work\dev\msg-abaqus-toolkit\scripts\sg2DLaminateDB.py", line 140, in updateComboBox_1Sections
    for name, section in mdb.models[modelName].sections.items():
  File "SMAPyrModules\SMAPyrAbqRpoPy.m\src\abqRpoClient.py", line 536, in __call__
  File "SMAPyrModules\SMAPyrAbqRpoPy.m\src\abqRpoClient.py", line 946, in call
  File "SMAPyrModules\SMAPyrAbqRpoPy.m\src\abqRpoClient.py", line 630, in __call__
  File "SMAPyrModules\SMAPyrAbqRpoPy.m\src\abqRpoClient.py", line 761, in __request
TypeError: Cannot create a consistent method resolution
order (MRO) for bases aba_type
